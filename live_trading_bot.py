#!/usr/bin/env python3
"""
Live Trading Bot with OANDA API Integration
Fetches live market data and provides trading signals using all 4 strategies
"""

import pandas as pd
import numpy as np
import requests
import time
import joblib
from datetime import datetime, timedelta
import json
from colorama import init, Fore, Back, Style
from tabulate import tabulate
import warnings
warnings.filterwarnings('ignore')

# Initialize colorama for Windows
init(autoreset=True)

# OANDA API Configuration
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"
ACCOUNT_ID = "101-004-********-001"
API_URL = "https://api-fxpractice.oanda.com"  # Practice environment
DEMO_MODE = True  # Set to False for live trading

# Trading pairs
PAIRS = [
    "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD",
    "AUD_CAD", "EUR_JPY", "GBP_JPY", "USD_CHF", "EUR_GBP"
]

class LiveTradingBot:
    def __init__(self):
        self.load_models()
        self.headers = {
            "Authorization": f"Bearer {ACCESS_TOKEN}",
            "Content-Type": "application/json"
        }
        
    def load_models(self):
        """Load trained models and preprocessing components"""
        try:
            print(f"{Fore.CYAN}🤖 Loading trained models...")
            self.ensemble_model = joblib.load('trained_models/ensemble_four_strategy_model.pkl')
            self.scaler = joblib.load('trained_models/four_strategy_scaler.pkl')
            self.label_encoder = joblib.load('trained_models/four_strategy_label_encoder.pkl')
            self.feature_cols = joblib.load('trained_models/four_strategy_features.pkl')
            print(f"{Fore.GREEN}✅ Models loaded successfully!")
        except Exception as e:
            print(f"{Fore.RED}❌ Error loading models: {e}")
            raise
    
    def get_live_data(self, pair, count=100):
        """Fetch live market data from OANDA or generate demo data"""
        if DEMO_MODE:
            return self.generate_demo_data(pair, count)

        try:
            url = f"{API_URL}/v3/instruments/{pair}/candles"
            params = {
                "count": count,
                "granularity": "M1",  # 1-minute candles
                "price": "M"  # Mid prices
            }

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                candles = data['candles']

                # Convert to DataFrame
                df_data = []
                for candle in candles:
                    if candle['complete']:  # Only use complete candles
                        df_data.append({
                            'time': candle['time'],
                            'open': float(candle['mid']['o']),
                            'high': float(candle['mid']['h']),
                            'low': float(candle['mid']['l']),
                            'close': float(candle['mid']['c']),
                            'volume': int(candle['volume'])
                        })

                df = pd.DataFrame(df_data)
                df['time'] = pd.to_datetime(df['time'])
                return df
            else:
                print(f"{Fore.YELLOW}⚠️  API Error {response.status_code} for {pair}, switching to demo mode...")
                return self.generate_demo_data(pair, count)

        except Exception as e:
            print(f"{Fore.YELLOW}⚠️  API Error for {pair}: {e}, using demo data...")
            return self.generate_demo_data(pair, count)

    def generate_demo_data(self, pair, count):
        """Generate realistic demo market data"""
        # Base prices for different pairs
        base_prices = {
            'EUR_USD': 1.0850, 'GBP_USD': 1.2650, 'USD_JPY': 149.50,
            'AUD_USD': 0.6750, 'USD_CAD': 1.3450, 'AUD_CAD': 0.9080,
            'EUR_JPY': 162.30, 'GBP_JPY': 189.20, 'USD_CHF': 0.8950,
            'EUR_GBP': 0.8580
        }

        base_price = base_prices.get(pair, 1.0000)

        # Generate realistic price movements
        np.random.seed(int(time.time()) % 1000)  # Semi-random seed

        data = []
        current_time = datetime.now() - timedelta(minutes=count)

        for i in range(count):
            # Generate realistic OHLCV data
            price_change = np.random.normal(0, 0.0005)  # Small random changes
            open_price = base_price + price_change

            high_low_range = abs(np.random.normal(0, 0.0003))
            high_price = open_price + high_low_range
            low_price = open_price - high_low_range

            close_change = np.random.normal(0, 0.0002)
            close_price = max(low_price, min(high_price, open_price + close_change))

            volume = int(np.random.normal(1000, 200))

            data.append({
                'time': current_time + timedelta(minutes=i),
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': max(100, volume)
            })

            base_price = close_price  # Use close as next base

        return pd.DataFrame(data)
    
    def calculate_technical_indicators(self, df):
        """Calculate technical indicators for the data"""
        if len(df) < 50:
            return None
            
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_sma'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_sma'] + (bb_std * 2)
        df['bb_lower'] = df['bb_sma'] - (bb_std * 2)
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_position'] = (df['close'] - df['bb_lower']) / df['bb_width']
        
        # EMAs
        df['ema_20'] = df['close'].ewm(span=20).mean()
        df['ema_50'] = df['close'].ewm(span=50).mean()
        
        # Price features
        df['price_change'] = df['close'].pct_change()
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # Volume features
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Direction
        df['direction'] = np.where(df['close'] > df['open'], 1, 0)  # 1 for bullish, 0 for bearish

        # Trend (based on EMA relationship)
        df['trend'] = np.where(df['ema_20'] > df['ema_50'], 1, 0)  # 1 for uptrend, 0 for downtrend

        return df
    
    def check_strategy_signals(self, df, pair):
        """Check all strategies for signals"""
        if df is None or len(df) < 50:
            return None, None, None
            
        # Get the latest complete candle
        latest_data = df.iloc[-1:].copy()
        
        # Prepare features for prediction
        try:
            X = latest_data[self.feature_cols].fillna(0)
            X_scaled = self.scaler.transform(X)
            
            # Get prediction and confidence
            prediction = self.ensemble_model.predict(X_scaled)[0]
            prediction_proba = self.ensemble_model.predict_proba(X_scaled)[0]
            
            # Convert prediction back to label
            signal = self.label_encoder.inverse_transform([prediction])[0]
            confidence = max(prediction_proba) * 100
            
            # Get current price
            current_price = latest_data['close'].iloc[0]
            
            return signal, confidence, current_price
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error checking signals for {pair}: {e}")
            return None, None, None
    
    def display_signal(self, pair, signal, confidence, price, strategy="ENSEMBLE"):
        """Display trading signal in a formatted way"""
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        time_str = now.strftime("%H:%M:%S")
        
        if signal in ['BUY', 'SELL']:
            color = Fore.GREEN if signal == 'BUY' else Fore.RED
            print(f"{color}📊 {date_str} | {time_str} | {pair} | {signal} | ${price:.5f} | {confidence:.1f}% | {strategy}")
        else:
            print(f"{Fore.YELLOW}📊 {date_str} | {time_str} | {pair} | NO SIGNAL | ${price:.5f}")
    
    def run_live_trading(self):
        """Main live trading loop"""
        print(f"{Fore.CYAN}{Style.BRIGHT}🚀 LIVE TRADING BOT STARTED")
        print(f"{Fore.CYAN}{'='*80}")
        print(f"{Fore.WHITE}Monitoring {len(PAIRS)} pairs every 2 seconds before next candle...")
        print(f"{Fore.WHITE}Pairs: {', '.join(PAIRS)}")
        print(f"{Fore.CYAN}{'='*80}")
        
        while True:
            try:
                # Calculate time until next minute
                now = datetime.now()
                seconds_until_next_minute = 60 - now.second
                
                # Wait until 2 seconds before next minute
                if seconds_until_next_minute > 2:
                    time.sleep(seconds_until_next_minute - 2)
                
                print(f"\n{Fore.MAGENTA}🔍 Scanning for signals at {now.strftime('%H:%M:%S')}...")
                
                # Check all pairs
                signals_found = False
                for pair in PAIRS:
                    df = self.get_live_data(pair, count=100)
                    if df is not None:
                        df = self.calculate_technical_indicators(df)
                        signal, confidence, price = self.check_strategy_signals(df, pair)
                        
                        if signal in ['BUY', 'SELL']:
                            signals_found = True
                            self.display_signal(pair, signal, confidence, price)
                        else:
                            self.display_signal(pair, "NO SIGNAL", 0, price if price else 0)
                    
                    time.sleep(0.1)  # Small delay between pairs
                
                if not signals_found:
                    print(f"{Fore.YELLOW}📊 No trading signals found in this scan")
                
                # Wait for next scan (approximately 1 minute)
                time.sleep(58)
                
            except KeyboardInterrupt:
                print(f"\n{Fore.CYAN}👋 Trading bot stopped by user")
                break
            except Exception as e:
                print(f"{Fore.RED}❌ Error in main loop: {e}")
                time.sleep(5)

if __name__ == "__main__":
    bot = LiveTradingBot()
    bot.run_live_trading()
