#!/usr/bin/env python3
"""
Strategy Backtesting System
Tests individual strategies (S1, S2, S3, S4) on live market data
"""

import pandas as pd
import numpy as np
import requests
import joblib
from datetime import datetime, timedelta
from colorama import init, Fore, Back, Style
from tabulate import tabulate
import warnings
warnings.filterwarnings('ignore')

# Initialize colorama
init(autoreset=True)

# OANDA API Configuration
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"
ACCOUNT_ID = "101-004-********-001"
API_URL = "https://api-fxpractice.oanda.com"  # Practice environment
DEMO_MODE = True  # Set to False for live API

# Available pairs
AVAILABLE_PAIRS = [
    "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD",
    "AUD_CAD", "EUR_JPY", "GBP_JPY", "USD_CHF", "EUR_GBP"
]

class StrategyBacktester:
    def __init__(self):
        self.headers = {
            "Authorization": f"Bearer {ACCESS_TOKEN}",
            "Content-Type": "application/json"
        }
        self.load_strategy_models()
    
    def load_strategy_models(self):
        """Load individual strategy models"""
        try:
            print(f"{Fore.CYAN}🤖 Loading strategy models...")
            self.models = {
                'S1': joblib.load('trained_models/randomforest_four_strategy_model.pkl'),
                'S2': joblib.load('trained_models/gradientboosting_four_strategy_model.pkl'),
                'S3': joblib.load('trained_models/logisticregression_four_strategy_model.pkl'),
                'S4': joblib.load('trained_models/ensemble_four_strategy_model.pkl')
            }
            self.scaler = joblib.load('trained_models/four_strategy_scaler.pkl')
            self.label_encoder = joblib.load('trained_models/four_strategy_label_encoder.pkl')
            self.feature_cols = joblib.load('trained_models/four_strategy_features.pkl')
            print(f"{Fore.GREEN}✅ Strategy models loaded successfully!")
        except Exception as e:
            print(f"{Fore.RED}❌ Error loading models: {e}")
            raise
    
    def get_historical_data(self, pair, count):
        """Fetch historical data from OANDA or generate demo data"""
        if DEMO_MODE:
            return self.generate_demo_data(pair, count)

        try:
            url = f"{API_URL}/v3/instruments/{pair}/candles"
            params = {
                "count": count,
                "granularity": "M1",
                "price": "M"
            }

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                candles = data['candles']

                df_data = []
                for candle in candles:
                    if candle['complete']:
                        df_data.append({
                            'time': candle['time'],
                            'open': float(candle['mid']['o']),
                            'high': float(candle['mid']['h']),
                            'low': float(candle['mid']['l']),
                            'close': float(candle['mid']['c']),
                            'volume': int(candle['volume'])
                        })

                df = pd.DataFrame(df_data)
                df['time'] = pd.to_datetime(df['time'])
                return df
            else:
                print(f"{Fore.YELLOW}⚠️  API Error {response.status_code}, using demo data...")
                return self.generate_demo_data(pair, count)

        except Exception as e:
            print(f"{Fore.YELLOW}⚠️  API Error: {e}, using demo data...")
            return self.generate_demo_data(pair, count)

    def generate_demo_data(self, pair, count):
        """Generate realistic demo market data for backtesting"""
        # Base prices for different pairs
        base_prices = {
            'EUR_USD': 1.0850, 'GBP_USD': 1.2650, 'USD_JPY': 149.50,
            'AUD_USD': 0.6750, 'USD_CAD': 1.3450, 'AUD_CAD': 0.9080,
            'EUR_JPY': 162.30, 'GBP_JPY': 189.20, 'USD_CHF': 0.8950,
            'EUR_GBP': 0.8580
        }

        base_price = base_prices.get(pair, 1.0000)

        # Generate realistic price movements with trends
        np.random.seed(42)  # Fixed seed for consistent backtesting

        data = []
        current_time = datetime.now() - timedelta(minutes=count)

        # Add some trend and volatility
        trend = np.random.choice([-1, 0, 1], p=[0.3, 0.4, 0.3])  # Bearish, sideways, bullish
        trend_strength = np.random.uniform(0.0001, 0.0005)

        for i in range(count):
            # Add trend component
            trend_component = trend * trend_strength * (i / count)

            # Generate realistic OHLCV data
            price_change = np.random.normal(0, 0.0008) + trend_component
            open_price = base_price + price_change

            high_low_range = abs(np.random.normal(0, 0.0005))
            high_price = open_price + high_low_range
            low_price = open_price - high_low_range

            close_change = np.random.normal(0, 0.0003)
            close_price = max(low_price, min(high_price, open_price + close_change))

            volume = int(np.random.normal(1200, 300))

            data.append({
                'time': current_time + timedelta(minutes=i),
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': max(100, volume)
            })

            base_price = close_price  # Use close as next base

        return pd.DataFrame(data)
    
    def calculate_technical_indicators(self, df):
        """Calculate technical indicators"""
        if len(df) < 50:
            return None
            
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_sma'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_sma'] + (bb_std * 2)
        df['bb_lower'] = df['bb_sma'] - (bb_std * 2)
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_position'] = (df['close'] - df['bb_lower']) / df['bb_width']
        
        # EMAs
        df['ema_20'] = df['close'].ewm(span=20).mean()
        df['ema_50'] = df['close'].ewm(span=50).mean()
        
        # Price features
        df['price_change'] = df['close'].pct_change()
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # Volume features
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Direction
        df['direction'] = np.where(df['close'] > df['open'], 1, 0)

        # Trend (based on EMA relationship)
        df['trend'] = np.where(df['ema_20'] > df['ema_50'], 1, 0)  # 1 for uptrend, 0 for downtrend

        return df
    
    def backtest_strategy(self, df, strategy_name, pair):
        """Backtest a specific strategy"""
        if df is None or len(df) < 50:
            return None
            
        model = self.models[strategy_name]
        results = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'buy_wins': 0,
            'buy_losses': 0,
            'sell_wins': 0,
            'sell_losses': 0,
            'buy_accuracy': 0,
            'sell_accuracy': 0,
            'overall_accuracy': 0
        }
        
        # Test on each candle (except last 10 for future price checking)
        for i in range(50, len(df) - 10):
            try:
                # Get current candle data
                current_data = df.iloc[i:i+1].copy()
                X = current_data[self.feature_cols].fillna(0)
                X_scaled = self.scaler.transform(X)
                
                # Get prediction
                prediction = model.predict(X_scaled)[0]
                signal = self.label_encoder.inverse_transform([prediction])[0]
                
                if signal in ['BUY', 'SELL']:
                    results['total_signals'] += 1
                    
                    # Get current and future prices
                    current_price = df.iloc[i]['close']
                    future_price = df.iloc[i + 5]['close']  # 5 candles later
                    
                    if signal == 'BUY':
                        results['buy_signals'] += 1
                        if future_price > current_price:
                            results['buy_wins'] += 1
                        else:
                            results['buy_losses'] += 1
                    
                    elif signal == 'SELL':
                        results['sell_signals'] += 1
                        if future_price < current_price:
                            results['sell_wins'] += 1
                        else:
                            results['sell_losses'] += 1
                            
            except Exception as e:
                continue
        
        # Calculate accuracies
        if results['buy_signals'] > 0:
            results['buy_accuracy'] = (results['buy_wins'] / results['buy_signals']) * 100
        
        if results['sell_signals'] > 0:
            results['sell_accuracy'] = (results['sell_wins'] / results['sell_signals']) * 100
        
        if results['total_signals'] > 0:
            total_wins = results['buy_wins'] + results['sell_wins']
            results['overall_accuracy'] = (total_wins / results['total_signals']) * 100
        
        return results
    
    def display_results(self, strategy_results, pair):
        """Display backtest results in a colorful table format"""
        print(f"\n{Fore.CYAN}{Style.BRIGHT}📊 BACKTEST RESULTS FOR {pair}")
        print(f"{Fore.CYAN}{'='*80}")
        
        # Individual strategy results
        table_data = []
        total_buy_signals = 0
        total_sell_signals = 0
        total_buy_wins = 0
        total_sell_wins = 0
        
        for strategy, results in strategy_results.items():
            if results:
                table_data.append([
                    f"{Fore.YELLOW}{strategy}",
                    f"{Fore.WHITE}{results['total_signals']}",
                    f"{Fore.GREEN}{results['buy_signals']}",
                    f"{Fore.GREEN}{results['buy_wins']}/{results['buy_losses']}",
                    f"{Fore.GREEN}{results['buy_accuracy']:.1f}%",
                    f"{Fore.RED}{results['sell_signals']}",
                    f"{Fore.RED}{results['sell_wins']}/{results['sell_losses']}",
                    f"{Fore.RED}{results['sell_accuracy']:.1f}%",
                    f"{Fore.MAGENTA}{results['overall_accuracy']:.1f}%"
                ])
                
                total_buy_signals += results['buy_signals']
                total_sell_signals += results['sell_signals']
                total_buy_wins += results['buy_wins']
                total_sell_wins += results['sell_wins']
        
        headers = [
            f"{Fore.CYAN}Strategy",
            f"{Fore.CYAN}Total",
            f"{Fore.CYAN}Buy Signals",
            f"{Fore.CYAN}Buy W/L",
            f"{Fore.CYAN}Buy Acc%",
            f"{Fore.CYAN}Sell Signals",
            f"{Fore.CYAN}Sell W/L",
            f"{Fore.CYAN}Sell Acc%",
            f"{Fore.CYAN}Overall Acc%"
        ]
        
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
        
        # Overall summary
        if len(strategy_results) > 1:
            total_signals = total_buy_signals + total_sell_signals
            total_wins = total_buy_wins + total_sell_wins
            overall_accuracy = (total_wins / total_signals * 100) if total_signals > 0 else 0
            buy_accuracy = (total_buy_wins / total_buy_signals * 100) if total_buy_signals > 0 else 0
            sell_accuracy = (total_sell_wins / total_sell_signals * 100) if total_sell_signals > 0 else 0
            
            print(f"\n{Fore.CYAN}{Style.BRIGHT}🏆 OVERALL SUMMARY")
            print(f"{Fore.CYAN}{'='*50}")
            print(f"{Fore.GREEN}📈 Total BUY signals: {total_buy_signals} (Accuracy: {buy_accuracy:.1f}%)")
            print(f"{Fore.RED}📉 Total SELL signals: {total_sell_signals} (Accuracy: {sell_accuracy:.1f}%)")
            print(f"{Fore.MAGENTA}🎯 Overall Accuracy: {overall_accuracy:.1f}%")
    
    def run_backtest(self):
        """Main backtesting interface"""
        print(f"{Fore.CYAN}{Style.BRIGHT}🔬 STRATEGY BACKTESTING SYSTEM")
        print(f"{Fore.CYAN}{'='*80}")
        
        # Get user inputs
        try:
            candles = int(input(f"{Fore.YELLOW}Enter number of candles to backtest (50-5000): "))
            if candles < 50 or candles > 5000:
                print(f"{Fore.RED}❌ Invalid number of candles. Using 1000.")
                candles = 1000
        except:
            candles = 1000
        
        print(f"\n{Fore.CYAN}Available pairs:")
        for i, pair in enumerate(AVAILABLE_PAIRS, 1):
            print(f"{Fore.WHITE}{i}. {pair}")
        
        try:
            pair_choice = int(input(f"{Fore.YELLOW}Select pair (1-{len(AVAILABLE_PAIRS)}): ")) - 1
            if 0 <= pair_choice < len(AVAILABLE_PAIRS):
                selected_pair = AVAILABLE_PAIRS[pair_choice]
            else:
                selected_pair = "EUR_USD"
                print(f"{Fore.RED}❌ Invalid choice. Using EUR_USD.")
        except:
            selected_pair = "EUR_USD"
        
        print(f"\n{Fore.CYAN}Available strategies:")
        print(f"{Fore.WHITE}1. S1 (Breakout with Volume)")
        print(f"{Fore.WHITE}2. S2 (Order Block Strategy)")
        print(f"{Fore.WHITE}3. S3 (Support/Resistance Rejection)")
        print(f"{Fore.WHITE}4. S4 (Trendline Break with Rejection)")
        print(f"{Fore.WHITE}5. All Strategies")
        
        try:
            strategy_choice = input(f"{Fore.YELLOW}Select strategy (1-5 or comma-separated like 1,3,4): ")
            if strategy_choice == "5":
                selected_strategies = ['S1', 'S2', 'S3', 'S4']
            else:
                choices = [int(x.strip()) for x in strategy_choice.split(',')]
                strategy_map = {1: 'S1', 2: 'S2', 3: 'S3', 4: 'S4'}
                selected_strategies = [strategy_map[c] for c in choices if c in strategy_map]
        except:
            selected_strategies = ['S1', 'S2', 'S3', 'S4']
        
        print(f"\n{Fore.CYAN}🔄 Fetching {candles} candles for {selected_pair}...")
        df = self.get_historical_data(selected_pair, candles + 50)  # Extra for indicators
        
        if df is None:
            print(f"{Fore.RED}❌ Failed to fetch data")
            return
        
        print(f"{Fore.GREEN}✅ Data fetched successfully!")
        print(f"{Fore.CYAN}🔧 Calculating technical indicators...")
        df = self.calculate_technical_indicators(df)
        
        if df is None:
            print(f"{Fore.RED}❌ Failed to calculate indicators")
            return
        
        print(f"{Fore.CYAN}🧪 Running backtest...")
        
        # Run backtest for selected strategies
        results = {}
        for strategy in selected_strategies:
            print(f"{Fore.YELLOW}Testing {strategy}...")
            results[strategy] = self.backtest_strategy(df, strategy, selected_pair)
        
        # Display results
        self.display_results(results, selected_pair)

if __name__ == "__main__":
    backtester = StrategyBacktester()
    backtester.run_backtest()
