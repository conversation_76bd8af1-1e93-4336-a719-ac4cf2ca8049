#!/usr/bin/env python3
"""
Test OANDA API Connection
Check if we can fetch live market data
"""

import requests
import json
from datetime import datetime
from colorama import init, Fore, Back, Style

# Initialize colorama
init(autoreset=True)

# OANDA API Configuration
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"
ACCOUNT_ID = "101-004-********-001"

# Try different API endpoints
API_ENDPOINTS = [
    "https://api-fxtrade.oanda.com",  # Live environment
    "https://api-fxpractice.oanda.com"  # Practice environment
]

PAIRS = [
    "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD",
    "AUD_CAD", "EUR_JPY", "GBP_JPY", "USD_CHF", "EUR_GBP"
]

def test_api_connection():
    """Test OANDA API connection with different endpoints"""
    
    print(f"{Fore.CYAN}{Style.BRIGHT}🔍 TESTING OANDA API CONNECTION")
    print(f"{Fore.CYAN}{'='*60}")
    
    headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    for i, api_url in enumerate(API_ENDPOINTS, 1):
        env_name = "LIVE" if "fxtrade" in api_url else "PRACTICE"
        print(f"\n{Fore.YELLOW}🌐 Testing {env_name} Environment ({api_url})")
        print(f"{Fore.CYAN}{'-'*60}")
        
        # Test account info
        try:
            account_url = f"{api_url}/v3/accounts/{ACCOUNT_ID}"
            response = requests.get(account_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                account_data = response.json()
                print(f"{Fore.GREEN}✅ Account Access: SUCCESS")
                print(f"{Fore.WHITE}   Account ID: {ACCOUNT_ID}")
                if 'account' in account_data:
                    balance = account_data['account'].get('balance', 'N/A')
                    currency = account_data['account'].get('currency', 'N/A')
                    print(f"{Fore.WHITE}   Balance: {balance} {currency}")
            else:
                print(f"{Fore.RED}❌ Account Access: FAILED ({response.status_code})")
                print(f"{Fore.RED}   Response: {response.text[:100]}...")
                continue
                
        except Exception as e:
            print(f"{Fore.RED}❌ Account Access: ERROR - {e}")
            continue
        
        # Test market data for a few pairs
        print(f"\n{Fore.CYAN}📊 Testing Market Data:")
        successful_pairs = 0
        
        for pair in PAIRS[:3]:  # Test first 3 pairs
            try:
                candles_url = f"{api_url}/v3/instruments/{pair}/candles"
                params = {
                    "count": 5,
                    "granularity": "M1",
                    "price": "M"
                }
                
                response = requests.get(candles_url, headers=headers, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    candles = data.get('candles', [])
                    if candles:
                        latest_candle = candles[-1]
                        if 'mid' in latest_candle:
                            price = latest_candle['mid']['c']
                            time_str = latest_candle['time'][:19]
                            print(f"{Fore.GREEN}   ✅ {pair}: {price} (Time: {time_str})")
                            successful_pairs += 1
                        else:
                            print(f"{Fore.YELLOW}   ⚠️  {pair}: No price data")
                    else:
                        print(f"{Fore.YELLOW}   ⚠️  {pair}: No candles")
                else:
                    print(f"{Fore.RED}   ❌ {pair}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"{Fore.RED}   ❌ {pair}: {str(e)[:50]}...")
        
        if successful_pairs > 0:
            print(f"\n{Fore.GREEN}🎉 SUCCESS! Found working API endpoint:")
            print(f"{Fore.GREEN}   Environment: {env_name}")
            print(f"{Fore.GREEN}   URL: {api_url}")
            print(f"{Fore.GREEN}   Successful pairs: {successful_pairs}/3")
            return api_url, True
        else:
            print(f"\n{Fore.RED}❌ No market data available from {env_name}")
    
    print(f"\n{Fore.RED}💥 FAILED: No working API endpoint found")
    return None, False

def test_live_candle_fetch(api_url):
    """Test fetching current/running candle"""
    
    print(f"\n{Fore.CYAN}{Style.BRIGHT}🕐 TESTING LIVE CANDLE FETCH")
    print(f"{Fore.CYAN}{'='*60}")
    
    headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    pair = "EUR_USD"
    
    try:
        # Get latest candles including incomplete ones
        url = f"{api_url}/v3/instruments/{pair}/candles"
        params = {
            "count": 3,
            "granularity": "M1",
            "price": "M"
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            candles = data.get('candles', [])
            
            print(f"{Fore.GREEN}✅ Successfully fetched {len(candles)} candles for {pair}")
            
            for i, candle in enumerate(candles):
                complete_status = "✅ Complete" if candle.get('complete', False) else "🔄 Running"
                time_str = candle['time'][:19]
                price = candle['mid']['c']
                volume = candle.get('volume', 0)
                
                print(f"{Fore.WHITE}   Candle {i+1}: {complete_status}")
                print(f"{Fore.WHITE}     Time: {time_str}")
                print(f"{Fore.WHITE}     OHLC: {candle['mid']['o']} | {candle['mid']['h']} | {candle['mid']['l']} | {candle['mid']['c']}")
                print(f"{Fore.WHITE}     Volume: {volume}")
                print()
            
            # Check if we have a running candle
            running_candles = [c for c in candles if not c.get('complete', True)]
            if running_candles:
                print(f"{Fore.GREEN}🎯 Found {len(running_candles)} running candle(s) - Perfect for live trading!")
            else:
                print(f"{Fore.YELLOW}⚠️  All candles are complete - may need to adjust timing")
                
            return True
            
        else:
            print(f"{Fore.RED}❌ Failed to fetch candles: HTTP {response.status_code}")
            print(f"{Fore.RED}   Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"{Fore.RED}❌ Error fetching live candles: {e}")
        return False

def main():
    """Main test function"""
    
    print(f"{Fore.CYAN}{Style.BRIGHT}🚀 OANDA API CONNECTION TEST")
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.WHITE}Account ID: {ACCOUNT_ID}")
    print(f"{Fore.WHITE}Testing {len(PAIRS)} currency pairs")
    print(f"{Fore.WHITE}Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test API connection
    working_api, success = test_api_connection()
    
    if success and working_api:
        # Test live candle fetching
        candle_success = test_live_candle_fetch(working_api)
        
        if candle_success:
            print(f"\n{Fore.GREEN}{Style.BRIGHT}🎉 ALL TESTS PASSED!")
            print(f"{Fore.GREEN}{'='*60}")
            print(f"{Fore.GREEN}✅ API Connection: Working")
            print(f"{Fore.GREEN}✅ Market Data: Available")
            print(f"{Fore.GREEN}✅ Live Candles: Accessible")
            print(f"{Fore.GREEN}✅ Ready for Live Trading Bot!")
            
            # Save working configuration
            config = {
                "api_url": working_api,
                "access_token": ACCESS_TOKEN,
                "account_id": ACCOUNT_ID,
                "test_time": datetime.now().isoformat(),
                "status": "working"
            }
            
            with open("api_config.json", "w") as f:
                json.dump(config, f, indent=2)
            
            print(f"{Fore.CYAN}💾 Configuration saved to api_config.json")
            
        else:
            print(f"\n{Fore.YELLOW}⚠️  API works but candle fetching has issues")
    else:
        print(f"\n{Fore.RED}💥 API CONNECTION FAILED")
        print(f"{Fore.RED}Please check:")
        print(f"{Fore.RED}  - Access token validity")
        print(f"{Fore.RED}  - Account ID correctness")
        print(f"{Fore.RED}  - Internet connection")
        print(f"{Fore.RED}  - OANDA service status")

if __name__ == "__main__":
    main()
