# 🚀 Advanced Trading System

A comprehensive multi-strategy trading bot with live market data integration using OANDA API.

## 📋 Features

### 🔴 Live Trading Bot
- **Real-time monitoring** of 10 currency pairs
- **Signal detection** every 2 seconds before candle close
- **Multi-strategy analysis** using all 4 trained strategies
- **Colorful CLI interface** with clear signal display
- **Automatic data fetching** from OANDA API

### 🔬 Strategy Backtesting
- **Individual strategy testing** (S1, S2, S3, S4)
- **Historical performance analysis**
- **Detailed win/loss statistics**
- **Customizable backtest parameters**
- **Comprehensive result tables**

### 🎯 Model Training
- **Four distinct strategies**:
  - S1: Breakout with Volume
  - S2: Order Block Strategy  
  - S3: Support/Resistance Rejection
  - S4: Trendline Break with Rejection
- **Ensemble model** for best performance
- **Advanced machine learning** algorithms

## 🛠️ Installation

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Run the system:**
```bash
python trading_system.py
```

## 📊 Monitored Currency Pairs

- EUR/USD
- GBP/USD
- USD/JPY
- AUD/USD
- USD/CAD
- AUD/CAD
- EUR/JPY
- GBP/JPY
- USD/CHF
- EUR/GBP

## 🔧 Configuration

The system uses OANDA API with the following configuration:
- **Account ID**: 101-004-********-001
- **API Access**: Live trading environment
- **Data Frequency**: 1-minute candles
- **Scan Interval**: Every 2 seconds before candle close

## 📈 Usage

### Live Trading
1. Select option 1 from main menu
2. Bot will start monitoring all pairs
3. Signals displayed in format: `Date | Time | Pair | Direction | Price | Confidence | Strategy`
4. Press Ctrl+C to stop

### Backtesting
1. Select option 2 from main menu
2. Enter number of candles to test (50-5000)
3. Choose currency pair
4. Select strategy(ies) to test
5. View detailed results table

### Model Training
1. Select option 3 from main menu
2. System will retrain all strategies
3. New models saved automatically

## 📁 File Structure

```
├── trading_system.py              # Main launcher
├── live_trading_bot.py           # Live trading implementation
├── backtest_strategies.py        # Backtesting system
├── Complete_Four_Strategy_Training.py  # Model training
├── Strategy1_Labeling.py         # Strategy 1 implementation
├── Strategy2_Labeling.py         # Strategy 2 implementation
├── Strategy3_Labeling.py         # Strategy 3 implementation
├── Strategy4_Labeling.py         # Strategy 4 implementation
├── trained_models/               # Saved models directory
├── output_signals_S*.csv         # Strategy signal data
├── historical_data_*.csv         # Historical market data
└── requirements.txt              # Dependencies
```

## 🎯 Strategy Performance

Based on latest training results:
- **Ensemble Model**: 92.48% accuracy
- **Total Signals**: 13,289 actionable signals
- **Best Strategy**: S3 (Support/Resistance) with 9,017 signals
- **Most Conservative**: S2 (Order Block) with 298 signals

## ⚠️ Important Notes

- **Live trading**: Requires stable internet connection
- **API limits**: OANDA has rate limits for API calls
- **Risk management**: This is for educational purposes
- **Data quality**: Depends on OANDA data feed reliability

## 🔄 Updates

The system automatically:
- Fetches latest market data
- Applies technical indicators
- Runs all strategies simultaneously
- Provides confidence scores
- Logs all activities

## 📞 Support

For issues or questions:
1. Check OANDA API status
2. Verify internet connection
3. Ensure all dependencies are installed
4. Review error messages in console
