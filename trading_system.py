#!/usr/bin/env python3
"""
Main Trading System Launcher
Choose between Live Trading and Backtesting
"""

import subprocess
import sys
from colorama import init, Fore, Back, Style

# Initialize colorama
init(autoreset=True)

def display_banner():
    """Display system banner"""
    print(f"{Fore.CYAN}{Style.BRIGHT}")
    print("╔" + "═" * 78 + "╗")
    print("║" + " " * 20 + "🚀 ADVANCED TRADING SYSTEM 🚀" + " " * 27 + "║")
    print("║" + " " * 78 + "║")
    print("║" + " " * 15 + "Multi-Strategy Trading Bot with Live Data" + " " * 22 + "║")
    print("║" + " " * 25 + "Powered by OANDA API" + " " * 33 + "║")
    print("║" + " " * 30 + "🔧 DEMO MODE ACTIVE" + " " * 29 + "║")
    print("╚" + "═" * 78 + "╝")
    print(f"{Style.RESET_ALL}")

def display_menu():
    """Display main menu"""
    print(f"\n{Fore.YELLOW}{Style.BRIGHT}📋 MAIN MENU")
    print(f"{Fore.CYAN}{'='*50}")
    print(f"{Fore.WHITE}1. 📈 Live Trading Bot")
    print(f"{Fore.WHITE}   - Real-time signal detection")
    print(f"{Fore.WHITE}   - 10 currency pairs monitoring")
    print(f"{Fore.WHITE}   - Scans every 2 seconds before candle close")
    print(f"{Fore.WHITE}")
    print(f"{Fore.WHITE}2. 🔬 Strategy Backtesting")
    print(f"{Fore.WHITE}   - Test individual strategies (S1, S2, S3, S4)")
    print(f"{Fore.WHITE}   - Historical performance analysis")
    print(f"{Fore.WHITE}   - Detailed win/loss statistics")
    print(f"{Fore.WHITE}")
    print(f"{Fore.WHITE}3. 🎯 Train New Models")
    print(f"{Fore.WHITE}   - Retrain all four strategies")
    print(f"{Fore.WHITE}   - Update model with latest data")
    print(f"{Fore.WHITE}")
    print(f"{Fore.WHITE}4. ❌ Exit")
    print(f"{Fore.CYAN}{'='*50}")

def run_live_trading():
    """Launch live trading bot"""
    print(f"\n{Fore.GREEN}🚀 Starting Live Trading Bot...")
    print(f"{Fore.YELLOW}Press Ctrl+C to stop the bot")
    try:
        subprocess.run([sys.executable, "live_trading_bot.py"])
    except KeyboardInterrupt:
        print(f"\n{Fore.CYAN}👋 Live trading stopped")
    except Exception as e:
        print(f"{Fore.RED}❌ Error running live trading: {e}")

def run_backtesting():
    """Launch backtesting system"""
    print(f"\n{Fore.GREEN}🔬 Starting Backtesting System...")
    try:
        subprocess.run([sys.executable, "backtest_strategies.py"])
    except Exception as e:
        print(f"{Fore.RED}❌ Error running backtesting: {e}")

def run_training():
    """Launch model training"""
    print(f"\n{Fore.GREEN}🎯 Starting Model Training...")
    try:
        subprocess.run([sys.executable, "Complete_Four_Strategy_Training.py"])
    except Exception as e:
        print(f"{Fore.RED}❌ Error running training: {e}")

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['pandas', 'numpy', 'sklearn', 'requests', 'colorama', 'tabulate', 'joblib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"{Fore.RED}❌ Missing required packages: {', '.join(missing_packages)}")
        print(f"{Fore.YELLOW}💡 Install them using: pip install -r requirements.txt")
        return False
    return True

def main():
    """Main application loop"""
    display_banner()
    
    # Check dependencies
    if not check_dependencies():
        return
    
    while True:
        display_menu()
        
        try:
            choice = input(f"\n{Fore.YELLOW}Enter your choice (1-4): ").strip()
            
            if choice == "1":
                run_live_trading()
            elif choice == "2":
                run_backtesting()
            elif choice == "3":
                run_training()
            elif choice == "4":
                print(f"\n{Fore.CYAN}👋 Thank you for using the Advanced Trading System!")
                break
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter 1-4.")
                
        except KeyboardInterrupt:
            print(f"\n\n{Fore.CYAN}👋 Goodbye!")
            break
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {e}")

if __name__ == "__main__":
    main()
